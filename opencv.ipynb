{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"id": "lOmEWpCSvxrc"}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'cv2'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mModuleNotFoundError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mcv2\u001b[39;00m\n\u001b[32m      2\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtime\u001b[39;00m\n\u001b[32m      3\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mimutils\u001b[39;00m\n", "\u001b[31mModuleNotFoundError\u001b[39m: No module named 'cv2'"]}], "source": ["import cv2\n", "import time\n", "import imutils"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "mf-qTgVBv2p4"}, "outputs": [], "source": ["cam = cv2.VideoCapture(0)\n", "time.sleep(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "NujUVLHLv8ml"}, "outputs": [], "source": ["firstFrame = None\n", "area = 500"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "CQPcmD4Av-4o"}, "outputs": [], "source": ["while True:\n", "  _, img = cam.read()\n", "  if img is None:\n", "    break\n", "\n", "  text = \"Normal\"\n", "  img = imutils.resize(img, width=500)\n", "  grayImg = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)\n", "  gaussianImg = cv2.GaussianBlur(grayImg, (21, 21), 0)\n", "\n", "  if first<PERSON>rame is None:\n", "    firstFrame = gaussianImg\n", "    continue\n", "\n", "  imgDiff = cv2.absdiff(firstFrame, gaussianImg)\n", "\n", "  threshImg = cv2.threshold(imgDiff, 25, 255, cv2.THRESH_BINARY)[1]\n", "  threshImg = cv2.dilate(threshImg, None, iterations=2)\n", "\n", "  cnts = cv2.findContours(threshImg.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)\n", "  cnts = imutils.grab_contours(cnts)\n", "  for c in cnts:\n", "    if cv2.contourArea(c) < area:\n", "      continue\n", "    (x, y, w, h) = cv2.boundingRect(c)\n", "    cv2.rectangle(img, (x, y), (x + w, y + h), (0, 255, 0), 2)\n", "    text = \"Moving Object detected\"\n", "  print(text)\n", "  cv2.putText(img, text, (10, 20), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)\n", "  cv2.imshow(\"cameraFeed\", img)\n", "  key = cv2.<PERSON><PERSON><PERSON>(10)\n", "  if key == ord(\"q\"):\n", "    break\n", "\n", "cam.release()\n", "cv2.destroyAllWindows()"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 0}